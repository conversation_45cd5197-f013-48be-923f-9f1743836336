import { useCallback, useState, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { useRealTimeAnalytics } from './useRealTimeAnalytics';
import { useCourseOptimisticUpdates } from './useCourseOptimisticUpdates';
import { useLectureOptimisticUpdates } from './useLectureOptimisticUpdates';
import { ICourse } from '@/types/course';
import { ILecture } from '@/redux/features/lecture/lectureSlice';
import { toast } from 'sonner';

interface DashboardState {
  courses: ICourse[];
  lectures: { [courseId: string]: ILecture[] };
  stats: {
    totalCourses: number;
    totalLectures: number;
    totalStudents: number;
    totalEarnings: number;
    lastUpdated: Date;
  };
  isLoading: boolean;
  error: string | null;
}

interface UseRealTimeDashboardOptions {
  enableOptimisticUpdates?: boolean;
  enableRealTimeSync?: boolean;
  pollingInterval?: number;
}

export const useRealTimeDashboard = (options: UseRealTimeDashboardOptions = {}) => {
  const {
    enableOptimisticUpdates = true,
    enableRealTimeSync = true,
    pollingInterval = 30000
  } = options;

  const { user } = useSelector((state: RootState) => state.auth);
  const [dashboardState, setDashboardState] = useState<DashboardState>({
    courses: [],
    lectures: {},
    stats: {
      totalCourses: 0,
      totalLectures: 0,
      totalStudents: 0,
      totalEarnings: 0,
      lastUpdated: new Date()
    },
    isLoading: false,
    error: null
  });

  const stateRef = useRef(dashboardState);
  stateRef.current = dashboardState;

  // Real-time analytics connection
  const { isConnected, sendMessage } = useRealTimeAnalytics({
    enableWebSocket: enableRealTimeSync,
    pollingInterval,
    onUpdate: handleRealTimeUpdate
  });

  // Course optimistic updates
  const courseOptimistic = useCourseOptimisticUpdates({
    courses: dashboardState.courses,
    onCoursesChange: (courses) => {
      setDashboardState(prev => ({
        ...prev,
        courses,
        stats: {
          ...prev.stats,
          totalCourses: courses.length,
          lastUpdated: new Date()
        }
      }));
    },
    enableRealTime: enableRealTimeSync
  });

  // Handle real-time updates from WebSocket
  function handleRealTimeUpdate(update: any) {
    const { type, data } = update;

    switch (type) {
      case 'course-update':
        handleCourseUpdate(data);
        break;
      case 'lecture-update':
        handleLectureUpdate(data);
        break;
      case 'enrollment':
        handleEnrollmentUpdate(data);
        break;
      case 'revenue':
        handleRevenueUpdate(data);
        break;
      case 'analytics':
        handleAnalyticsUpdate(data);
        break;
    }
  }

  const handleCourseUpdate = useCallback((data: any) => {
    const { action, course, courses } = data;
    
    setDashboardState(prev => {
      let updatedCourses = [...prev.courses];
      
      switch (action) {
        case 'created':
          if (course && !updatedCourses.find(c => c._id === course._id)) {
            updatedCourses = [...updatedCourses, course];
            toast.success('New course created!', {
              description: `"${course.title}" has been added to your courses.`
            });
          }
          break;
        case 'updated':
          if (course) {
            updatedCourses = updatedCourses.map(c => 
              c._id === course._id ? { ...c, ...course } : c
            );
            toast.success('Course updated!', {
              description: `"${course.title}" has been updated.`
            });
          }
          break;
        case 'deleted':
          if (course?._id) {
            updatedCourses = updatedCourses.filter(c => c._id !== course._id);
            toast.success('Course deleted!', {
              description: 'Course has been removed from your dashboard.'
            });
          }
          break;
        case 'bulk-update':
          if (courses) {
            updatedCourses = courses;
          }
          break;
      }

      return {
        ...prev,
        courses: updatedCourses,
        stats: {
          ...prev.stats,
          totalCourses: updatedCourses.length,
          lastUpdated: new Date()
        }
      };
    });
  }, []);

  const handleLectureUpdate = useCallback((data: any) => {
    const { action, lecture, lectures, courseId } = data;
    
    setDashboardState(prev => {
      const updatedLectures = { ...prev.lectures };
      
      switch (action) {
        case 'created':
          if (lecture && courseId) {
            const courseLectures = updatedLectures[courseId] || [];
            if (!courseLectures.find(l => l._id === lecture._id)) {
              updatedLectures[courseId] = [...courseLectures, lecture];
            }
          }
          break;
        case 'updated':
          if (lecture && courseId) {
            const courseLectures = updatedLectures[courseId] || [];
            updatedLectures[courseId] = courseLectures.map(l => 
              l._id === lecture._id ? { ...l, ...lecture } : l
            );
          }
          break;
        case 'deleted':
          if (lecture?._id && courseId) {
            const courseLectures = updatedLectures[courseId] || [];
            updatedLectures[courseId] = courseLectures.filter(l => l._id !== lecture._id);
          }
          break;
        case 'reordered':
          if (lectures && courseId) {
            updatedLectures[courseId] = lectures;
          }
          break;
      }

      const totalLectures = Object.values(updatedLectures).reduce(
        (total, courseLectures) => total + courseLectures.length, 0
      );

      return {
        ...prev,
        lectures: updatedLectures,
        stats: {
          ...prev.stats,
          totalLectures,
          lastUpdated: new Date()
        }
      };
    });
  }, []);

  const handleEnrollmentUpdate = useCallback((data: any) => {
    const { courseId, studentCount, totalStudents } = data;
    
    setDashboardState(prev => {
      const updatedCourses = prev.courses.map(course => 
        course._id === courseId 
          ? { ...course, enrolledStudents: Array(studentCount).fill('') }
          : course
      );

      return {
        ...prev,
        courses: updatedCourses,
        stats: {
          ...prev.stats,
          totalStudents: totalStudents || prev.stats.totalStudents,
          lastUpdated: new Date()
        }
      };
    });

    toast.success('New enrollment!', {
      description: 'A student has enrolled in one of your courses.'
    });
  }, []);

  const handleRevenueUpdate = useCallback((data: any) => {
    const { amount, totalEarnings } = data;
    
    setDashboardState(prev => ({
      ...prev,
      stats: {
        ...prev.stats,
        totalEarnings: totalEarnings || prev.stats.totalEarnings + amount,
        lastUpdated: new Date()
      }
    }));

    toast.success('Payment received!', {
      description: `You earned $${amount} from a course purchase.`
    });
  }, []);

  const handleAnalyticsUpdate = useCallback((data: any) => {
    setDashboardState(prev => ({
      ...prev,
      stats: {
        ...prev.stats,
        ...data,
        lastUpdated: new Date()
      }
    }));
  }, []);

  // Initialize dashboard data
  const initializeDashboard = useCallback((initialData: Partial<DashboardState>) => {
    setDashboardState(prev => ({
      ...prev,
      ...initialData,
      stats: {
        ...prev.stats,
        ...initialData.stats,
        lastUpdated: new Date()
      }
    }));
  }, []);

  // Manual refresh function
  const refreshDashboard = useCallback(async () => {
    setDashboardState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      // This would trigger refetch of all dashboard data
      // The actual implementation depends on your API structure
      
      setDashboardState(prev => ({
        ...prev,
        isLoading: false,
        stats: {
          ...prev.stats,
          lastUpdated: new Date()
        }
      }));
      
      toast.success('Dashboard refreshed!');
    } catch (error) {
      setDashboardState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to refresh dashboard'
      }));
      
      toast.error('Failed to refresh dashboard');
    }
  }, []);

  // Get lecture optimistic updates for a specific course
  const getLectureOptimistic = useCallback((courseId: string) => {
    return useLectureOptimisticUpdates({
      courseId,
      onLecturesChange: (lectures) => {
        setDashboardState(prev => ({
          ...prev,
          lectures: {
            ...prev.lectures,
            [courseId]: lectures
          },
          stats: {
            ...prev.stats,
            totalLectures: Object.values({
              ...prev.lectures,
              [courseId]: lectures
            }).reduce((total, courseLectures) => total + courseLectures.length, 0),
            lastUpdated: new Date()
          }
        }));
      },
      enableRealTime: enableRealTimeSync
    });
  }, [enableRealTimeSync]);

  return {
    // State
    dashboardState,
    isRealTimeConnected: isConnected,
    
    // Actions
    initializeDashboard,
    refreshDashboard,
    
    // Optimistic updates
    courseOptimistic: enableOptimisticUpdates ? courseOptimistic : null,
    getLectureOptimistic: enableOptimisticUpdates ? getLectureOptimistic : null,
    
    // Real-time status
    connectionStatus: {
      isConnected,
      lastUpdate: dashboardState.stats.lastUpdated,
      hasError: !!dashboardState.error
    }
  };
};
