2025-06-24 00:01:16:116 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 00:03:54:354 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 00:06:16:616 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 00:09:57:957 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 00:11:17:1117 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 00:16:00:160 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 00:17:16:1716 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 00:20:51:2051 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/engagement-metrics - 400
2025-06-24 00:20:51:2051 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/enrollment-statistics - 400
2025-06-24 00:20:51:2051 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/revenue-detailed - 400
2025-06-24 00:20:51:2051 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/performance-detailed - 400
2025-06-24 00:20:53:2053 [ERROR]: ❌ Request error: GET /api/v1/stripe-connect/account-status - 404
2025-06-24 00:21:04:214 [ERROR]: ❌ Request error: POST /api/v1/stripe-connect/create-account - 404
2025-06-24 00:21:04:214 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 00:23:26:2326 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 00:23:35:2335 [ERROR]: ❌ Request error: POST /api/v1/stripe-connect/create-account - 404
2025-06-24 11:23:09:239 [ERROR]: ❌ Request error: GET /api/v1/users/me - 401
2025-06-24 11:23:09:239 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 401
2025-06-24 11:23:09:239 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/enrollment-statistics - 401
2025-06-24 11:23:09:239 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/engagement-metrics - 401
2025-06-24 11:23:09:239 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/performance-detailed - 401
2025-06-24 11:23:09:239 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/revenue-detailed - 401
2025-06-24 11:23:11:2311 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 11:23:11:2311 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/engagement-metrics - 400
2025-06-24 11:23:11:2311 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/enrollment-statistics - 400
2025-06-24 11:23:11:2311 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/performance-detailed - 400
2025-06-24 11:23:11:2311 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/revenue-detailed - 400
2025-06-24 11:23:11:2311 [ERROR]: ❌ Request error: GET /api/v1/stripe-connect/account-status - 404
2025-06-24 11:23:11:2311 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 11:23:17:2317 [ERROR]: ❌ Request error: POST /api/v1/stripe-connect/create-account - 404
2025-06-24 11:23:26:2326 [ERROR]: ❌ Request error: POST /api/v1/stripe-connect/create-account - 400
2025-06-24 11:24:06:246 [ERROR]: ❌ Request error: POST /api/v1/stripe-connect/create-account - 400
2025-06-24 11:24:30:2430 [ERROR]: ❌ Request error: POST /api/v1/stripe-connect/create-account - 404
2025-06-24 11:25:17:2517 [ERROR]: ❌ Request error: POST /api/v1/stripe-connect/create-account - 404
2025-06-24 11:25:28:2528 [ERROR]: ❌ Request error: POST /api/v1/stripe-connect/create-account - 404
2025-06-24 11:28:24:2824 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 11:29:28:2928 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 11:33:24:3324 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 11:35:28:3528 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 11:38:24:3824 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 11:41:28:4128 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 11:43:24:4324 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 11:47:28:4728 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 11:48:24:4824 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 11:53:24:5324 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 11:53:29:5329 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 11:58:24:5824 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 11:59:29:5929 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 12:03:24:324 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 12:05:29:529 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 12:08:24:824 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 12:11:29:1129 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 12:13:24:1324 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 12:18:40:1840 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 12:19:31:1931 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 12:24:25:2425 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 12:24:30:2430 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/enrollment-statistics - 400
2025-06-24 12:24:30:2430 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/engagement-metrics - 400
2025-06-24 12:24:30:2430 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/revenue-detailed - 400
2025-06-24 12:24:30:2430 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/performance-detailed - 400
2025-06-24 12:24:31:2431 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 12:29:35:2935 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 12:29:37:2937 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 12:39:51:3951 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 12:39:52:3952 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/enrollment-statistics - 400
2025-06-24 12:39:52:3952 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/engagement-metrics - 400
2025-06-24 12:39:52:3952 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/revenue-detailed - 400
2025-06-24 12:39:52:3952 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/performance-detailed - 400
2025-06-24 12:39:53:3953 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 12:40:16:4016 [ERROR]: ❌ Request error: POST /api/v1/stripe-connect/create-account-link - 400
2025-06-24 12:46:21:4621 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 12:46:21:4621 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 13:10:07:107 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 13:10:21:1021 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 13:15:07:157 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 13:16:21:1621 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 13:20:38:2038 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 13:20:39:2039 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/enrollment-statistics - 400
2025-06-24 13:20:39:2039 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/engagement-metrics - 400
2025-06-24 13:20:39:2039 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/revenue-detailed - 400
2025-06-24 13:20:39:2039 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/performance-detailed - 400
2025-06-24 13:22:41:2241 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 13:24:53:2453 [ERROR]: ❌ Request error: POST /api/v1/stripe-connect/create-account - 400
2025-06-24 13:26:18:2618 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 13:29:20:2920 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 13:31:18:3118 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 13:35:23:3523 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 13:36:38:3638 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 13:41:26:4126 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 13:41:38:4138 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 13:46:38:4638 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 13:47:29:4729 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 13:51:38:5138 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 13:53:32:5332 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 13:56:38:5638 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 13:59:34:5934 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 14:02:17:217 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 14:05:34:534 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 14:07:17:717 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 14:11:35:1135 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 14:12:17:1217 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 14:17:17:1717 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 14:17:35:1735 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 15:04:07:47 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 15:05:09:59 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 15:09:07:97 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 15:11:10:1110 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 15:12:53:1253 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/enrollment-statistics - 400
2025-06-24 15:12:53:1253 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/engagement-metrics - 400
2025-06-24 15:12:53:1253 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/revenue-detailed - 400
2025-06-24 15:12:53:1253 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/performance-detailed - 400
2025-06-24 15:14:52:1452 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 15:17:38:1738 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 15:20:21:2021 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 15:22:27:2227 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/enrollment-statistics - 400
2025-06-24 15:22:27:2227 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/engagement-metrics - 400
2025-06-24 15:22:27:2227 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/revenue-detailed - 400
2025-06-24 15:22:27:2227 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/performance-detailed - 400
2025-06-24 15:23:47:2347 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 15:23:48:2348 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 15:24:04:244 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 15:24:04:244 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 15:24:19:2419 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 15:24:50:2450 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 15:25:27:2527 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 15:25:37:2537 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 15:25:37:2537 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 15:25:53:2553 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 15:25:55:2555 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 15:26:05:265 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 15:27:25:2725 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 15:27:25:2725 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 15:42:28:4228 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 15:42:29:4229 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/enrollment-statistics - 400
2025-06-24 15:42:29:4229 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/engagement-metrics - 400
2025-06-24 15:42:29:4229 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/revenue-detailed - 400
2025-06-24 15:42:29:4229 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/performance-detailed - 400
2025-06-24 15:42:29:4229 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 15:45:02:452 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 15:48:27:4827 [ERROR]: ❌ Request error: GET /api/v1/messages/users/68592c277f6a4cce4c016883/threads - 404
2025-06-24 15:48:27:4827 [ERROR]: ❌ Request error: GET /api/v1/messaging/users/68592c277f6a4cce4c016883/folders - 403
2025-06-24 15:48:27:4827 [ERROR]: ❌ Request error: GET /api/v1/messaging/users/68592c277f6a4cce4c016883/stats - 403
2025-06-24 15:48:30:4830 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 15:48:31:4831 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/enrollment-statistics - 400
2025-06-24 15:48:31:4831 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/engagement-metrics - 400
2025-06-24 15:48:31:4831 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/revenue-detailed - 400
2025-06-24 15:48:31:4831 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/performance-detailed - 400
2025-06-24 15:48:31:4831 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 15:49:21:4921 [ERROR]: ❌ Request error: GET /api/v1/payments/payouts/[object%20Object] - 500
2025-06-24 15:58:58:5858 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 15:59:07:597 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 15:59:14:5914 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 15:59:15:5915 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 15:59:19:5919 [ERROR]: ❌ Request error: POST /api/v1/courses/create-course/68592c277f6a4cce4c016883 - 400
2025-06-24 15:59:24:5924 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 15:59:33:5933 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 15:59:42:5942 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 15:59:52:5952 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 15:59:53:5953 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 16:00:02:02 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 16:01:07:17 [ERROR]: ❌ Request error: POST /api/v1/courses/create-course/68592c277f6a4cce4c016883 - 400
2025-06-24 16:13:52:1352 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 16:13:52:1352 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 16:13:52:1352 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/engagement-metrics - 400
2025-06-24 16:13:52:1352 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/enrollment-statistics - 400
2025-06-24 16:13:52:1352 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/revenue-detailed - 400
2025-06-24 16:13:52:1352 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/performance-detailed - 400
2025-06-24 16:14:10:1410 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 16:17:14:1714 [ERROR]: ❌ Request error: GET /api/v1/messages/users/68592c277f6a4cce4c016883/threads - 404
2025-06-24 16:17:14:1714 [ERROR]: ❌ Request error: GET /api/v1/messaging/users/68592c277f6a4cce4c016883/folders - 403
2025-06-24 16:17:14:1714 [ERROR]: ❌ Request error: GET /api/v1/messaging/users/68592c277f6a4cce4c016883/stats - 403
2025-06-24 16:23:57:2357 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 16:24:15:2415 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 16:24:16:2416 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 16:24:42:2442 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 16:24:52:2452 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 16:25:05:255 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 16:25:37:2537 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 16:25:38:2538 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 16:25:45:2545 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 16:26:01:261 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 16:26:12:2612 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 16:26:13:2613 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 16:26:19:2619 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/engagement-metrics - 400
2025-06-24 16:26:19:2619 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/enrollment-statistics - 400
2025-06-24 16:26:19:2619 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/performance-detailed - 400
2025-06-24 16:26:19:2619 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/revenue-detailed - 400
2025-06-24 16:26:19:2619 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 16:36:44:3644 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 16:36:44:3644 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/enrollment-statistics - 400
2025-06-24 16:36:44:3644 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/engagement-metrics - 400
2025-06-24 16:36:44:3644 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/revenue-detailed - 400
2025-06-24 16:36:44:3644 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/performance-detailed - 400
2025-06-24 16:36:45:3645 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 16:41:50:4150 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 16:41:51:4151 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 16:52:31:5231 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 16:52:32:5232 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/enrollment-statistics - 400
2025-06-24 16:52:32:5232 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/revenue-detailed - 400
2025-06-24 16:52:33:5233 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 16:52:33:5233 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/engagement-metrics - 400
2025-06-24 16:52:33:5233 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/performance-detailed - 400
2025-06-24 16:58:16:5816 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 16:58:29:5829 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 16:58:36:5836 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 16:58:36:5836 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 16:58:47:5847 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 16:59:57:5957 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 17:00:14:014 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 17:00:52:052 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 17:01:26:126 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 17:01:34:134 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 17:01:44:144 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 17:01:53:153 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 17:01:57:157 [ERROR]: ❌ Request error: GET /api/v1/users/me - 401
2025-06-24 17:01:57:157 [ERROR]: ❌ Request error: GET /api/v1/lectures/685a79d4ffcd5f6f3213c8fc/get-lectures - 401
2025-06-24 17:02:03:23 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 17:02:12:212 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 17:02:13:213 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 17:02:21:221 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 17:02:31:231 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 18:00:54:054 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 18:01:12:112 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 18:01:25:125 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 18:01:37:137 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 18:01:47:147 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 18:02:15:215 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 18:02:52:252 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 18:03:15:315 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 18:03:34:334 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 18:21:38:2138 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 18:22:17:2217 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 18:23:04:234 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 18:23:39:2339 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 18:25:12:2512 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 18:25:27:2527 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 18:25:37:2537 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 18:26:01:261 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 18:57:39:5739 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 18:57:46:5746 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 18:57:54:5754 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 18:58:25:5825 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 18:58:33:5833 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 18:58:42:5842 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 19:00:15:015 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 19:00:15:015 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 19:00:37:037 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 19:00:44:044 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 19:58:49:5849 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 20:00:53:053 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 20:01:02:12 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 20:02:34:234 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 20:02:41:241 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 20:02:48:248 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 20:02:48:248 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 20:03:33:333 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 20:04:08:48 [ERROR]: ❌ Request error: POST /webhook - 400
2025-06-24 20:17:56:1756 [ERROR]: ❌ Request error: GET /api/v1/courses/creator/68592c277f6a4cce4c016883 - 401
2025-06-24 20:17:56:1756 [ERROR]: ❌ Request error: GET /api/v1/stripe-connect/account-status - 401
2025-06-24 20:17:56:1756 [ERROR]: ❌ Request error: GET /api/v1/payments/upcoming-payout/68592c277f6a4cce4c016883 - 401
2025-06-24 20:17:56:1756 [ERROR]: ❌ Request error: GET /api/v1/payments/earnings/68592c277f6a4cce4c016883 - 401
2025-06-24 20:17:56:1756 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 401
2025-06-24 20:17:56:1756 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/enrollment-statistics - 401
2025-06-24 20:17:56:1756 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/engagement-metrics - 401
2025-06-24 20:17:56:1756 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/performance-detailed - 401
2025-06-24 20:17:56:1756 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/revenue-detailed - 401
2025-06-24 20:17:58:1758 [ERROR]: ❌ Request error: POST /refresh-token - 401
2025-06-24 20:17:58:1758 [ERROR]: ❌ Request error: POST /refresh-token - 401
2025-06-24 20:17:58:1758 [ERROR]: ❌ Request error: POST /refresh-token - 401
2025-06-24 20:17:58:1758 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 20:17:58:1758 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/enrollment-statistics - 400
2025-06-24 20:18:21:1821 [ERROR]: ❌ Request error: GET /api/v1/users/me - 401
2025-06-24 20:18:25:1825 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/dashboard - 400
2025-06-24 20:18:25:1825 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/enrollment-statistics - 400
2025-06-24 20:18:25:1825 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/engagement-metrics - 400
2025-06-24 20:18:25:1825 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/performance-detailed - 400
2025-06-24 20:18:25:1825 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/activities - 400
2025-06-24 20:18:25:1825 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/revenue-detailed - 400
2025-06-24 20:18:32:1832 [ERROR]: ❌ Request error: GET /api/v1/messages/users/68592c277f6a4cce4c016883/threads - 404
2025-06-24 20:18:32:1832 [ERROR]: ❌ Request error: GET /api/v1/messaging/users/68592c277f6a4cce4c016883/folders - 403
2025-06-24 20:18:32:1832 [ERROR]: ❌ Request error: GET /api/v1/messaging/users/68592c277f6a4cce4c016883/stats - 403
2025-06-24 20:25:22:2522 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/dashboard - 400
2025-06-24 20:25:22:2522 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/activities - 400
2025-06-24 20:25:22:2522 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/revenue-detailed - 400
2025-06-24 20:25:22:2522 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/engagement-metrics - 400
2025-06-24 20:25:22:2522 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/enrollment-statistics - 400
2025-06-24 20:25:23:2523 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/performance-detailed - 400
2025-06-24 20:25:40:2540 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/dashboard - 400
2025-06-24 20:25:40:2540 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016883/activities - 400
2025-06-24 21:24:23:2423 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/activities - 401
2025-06-24 21:24:23:2423 [ERROR]: ❌ Request error: GET /api/v1/stripe-connect/account-status - 401
2025-06-24 21:24:23:2423 [ERROR]: ❌ Request error: GET /api/v1/payments/upcoming-payout/68592c277f6a4cce4c016883 - 401
2025-06-24 21:24:23:2423 [ERROR]: ❌ Request error: GET /api/v1/payments/earnings/68592c277f6a4cce4c016883 - 401
2025-06-24 21:24:23:2423 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/dashboard - 401
2025-06-24 21:24:23:2423 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/enrollment-statistics - 401
2025-06-24 21:24:23:2423 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/engagement-metrics - 401
2025-06-24 21:24:23:2423 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/revenue-detailed - 401
2025-06-24 21:24:23:2423 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/performance-detailed - 401
2025-06-24 21:24:26:2426 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/dashboard - 401
2025-06-24 21:24:26:2426 [ERROR]: ❌ Request error: GET /api/v1/users/me - 401
2025-06-24 21:24:26:2426 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/enrollment-statistics - 401
2025-06-24 21:24:26:2426 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/engagement-metrics - 401
2025-06-24 21:24:26:2426 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/revenue-detailed - 401
2025-06-24 21:24:26:2426 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/performance-detailed - 401
2025-06-24 21:24:40:2440 [ERROR]: ❌ Request error: GET /api/v1/users/me - 401
2025-06-24 21:24:43:2443 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/dashboard - 400
2025-06-24 21:24:44:2444 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/enrollment-statistics - 400
2025-06-24 21:24:44:2444 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/engagement-metrics - 400
2025-06-24 21:24:44:2444 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/revenue-detailed - 400
2025-06-24 21:24:44:2444 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/performance-detailed - 400
2025-06-24 21:24:44:2444 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/activities - 400
2025-06-24 21:30:03:303 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/dashboard - 400
2025-06-24 21:30:04:304 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/enrollment-statistics - 400
2025-06-24 21:30:04:304 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/performance-detailed - 400
2025-06-24 21:30:04:304 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/engagement-metrics - 400
2025-06-24 21:30:04:304 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/revenue-detailed - 400
2025-06-24 21:30:37:3037 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/activities - 400
2025-06-24 21:35:37:3537 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/dashboard - 400
2025-06-24 21:36:38:3638 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/activities - 400
2025-06-24 21:40:37:4037 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/dashboard - 400
2025-06-24 21:42:38:4238 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/activities - 400
2025-06-24 21:45:37:4537 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/dashboard - 400
2025-06-24 21:48:39:4839 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/activities - 400
2025-06-24 21:49:26:4926 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/enrollment-statistics - 400
2025-06-24 21:49:26:4926 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/performance-detailed - 400
2025-06-24 21:49:26:4926 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/revenue-detailed - 400
2025-06-24 21:49:27:4927 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/engagement-metrics - 400
2025-06-24 21:52:47:5247 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/68592c277f6a4cce4c016880/dashboard - 400
